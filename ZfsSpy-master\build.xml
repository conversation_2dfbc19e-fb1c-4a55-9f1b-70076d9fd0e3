<?xml version="1.0" encoding="UTF-8"?>
<project default="create_run_jar" name="Create Runnable Jar for Project ZFSSpy">
    <!--this file was created by Eclipse Runnable JAR Export Wizard-->
    <!--ANT 1.7 is required                                        -->
    <!--define folder properties-->
    <property name="dir.buildfile" value="."/>
    <property name="dir.workspace" value="${dir.buildfile}/.."/>
    <property name="dir.jarfile" value="${dir.buildfile}"/>
    <target name="create_run_jar">
        <jar destfile="${dir.jarfile}/ZfsSpy.jar" filesetmanifest="mergewithoutmain">
            <manifest>
                <attribute name="Main-Class" value="httpserver"/>
                <attribute name="Class-Path" value="."/>
            </manifest>
            <fileset dir="${dir.jarfile}/bin"/>
        </jar>
    </target>
</project>
